'use client'

import React, {
  useState, useEffect,
} from 'react';
import * as fabric from 'fabric';
import { Button } from '@/common/components/atoms';
import Link from 'next/link';
import { routes } from '@/common/routes';
import { ClientLogo } from '../header/ClientLogo';

interface CanvasWithV6Methods extends fabric.Canvas {
  insertAt: (index: number, ...objects: fabric.Object[]) => number;
}

interface CanvasHeaderProps {
  onClose: () => void;
  onSaveDesign: () => void;
  canvas?: fabric.Canvas | null;
}

export const CanvasHeader = ({
  onClose,
  onSaveDesign,
  canvas,
}: CanvasHeaderProps) => {
  const [selectedObject, setSelectedObject] = useState<fabric.Object | null>(null);
  const [canMoveUp, setCanMoveUp] = useState(false);
  const [canMoveDown, setCanMoveDown] = useState(false);

  useEffect(() => {
    if (!canvas) {
      return;
    }

    const updateSelection = () => {
      const activeObject = canvas.getActiveObject();
      setSelectedObject(activeObject || null);

      if (activeObject) {
        const objects = canvas.getObjects();
        const currentIndex = objects.indexOf(activeObject);
        setCanMoveUp(currentIndex < objects.length - 1);
        setCanMoveDown(currentIndex > 0);
      } else {
        setCanMoveUp(false);
        setCanMoveDown(false);
      }
    };

    canvas.on('selection:created', updateSelection);
    canvas.on('selection:updated', updateSelection);
    canvas.on('selection:cleared', updateSelection);
    canvas.on('object:added', updateSelection);
    canvas.on('object:removed', updateSelection);

    return () => {
      canvas.off('selection:created', updateSelection);
      canvas.off('selection:updated', updateSelection);
      canvas.off('selection:cleared', updateSelection);
      canvas.off('object:added', updateSelection);
      canvas.off('object:removed', updateSelection);
    };
  }, [canvas]);

  const moveLayerUp = () => {
    if (!canvas || !selectedObject) {
      return;
    }

    const objects = canvas.getObjects();
    const currentIndex = objects.indexOf(selectedObject);

    if (currentIndex < objects.length - 1) {
      canvas.remove(selectedObject);
      (canvas as CanvasWithV6Methods).insertAt(currentIndex + 1, selectedObject);
      canvas.renderAll();
    }

    const newObjects = canvas.getObjects();
    const newIndex = newObjects.indexOf(selectedObject);
    setCanMoveUp(newIndex < newObjects.length - 1);
    setCanMoveDown(newIndex > 0);
  };

  const moveLayerDown = () => {
    if (!canvas || !selectedObject) {
      return;
    }

    const objects = canvas.getObjects();
    const currentIndex = objects.indexOf(selectedObject);

    if (currentIndex > 0) {
      canvas.remove(selectedObject);
      (canvas as CanvasWithV6Methods).insertAt(currentIndex - 1, selectedObject);
      canvas.renderAll();
    }

    const newObjects = canvas.getObjects();
    const newIndex = newObjects.indexOf(selectedObject);
    setCanMoveUp(newIndex < newObjects.length - 1);
    setCanMoveDown(newIndex > 0);
  };

  return (
    <div className="bg-eerie-black border-b border-neutral-700 px-4 py-2 md:py-3 flex items-center justify-between relative">
      <div className="flex items-center gap-1 md:gap-2">
        <Link href={routes.homePath} prefetch={true} replace className="flex gap-1 md:gap-2 items-center text-white font-semibold text-xl md:text-2xl">
          <ClientLogo width={24} height={24}/>
        </Link>
        {selectedObject && (
          <>
            <Button
              onClick={moveLayerUp}
              disabled={!canMoveUp}
              variant="outline"
              size="sm"
              title="Bring to Front"
            >
              Bring to Front
            </Button>
            <Button
              onClick={moveLayerDown}
              disabled={!canMoveDown}
              variant="outline"
              size="sm"
              title="Send Back"
            >
              Send Back
            </Button>
          </>
        )}
      </div>
      <div className="absolute left-1/2 transform -translate-x-1/2 text-center">
        <h1 className="text-white font-semibold text-sm md:text-lg">Image Editor</h1>
      </div>
      <div className="flex items-center gap-1 md:gap-2">
        <Button
          variant="gradient"
          size="sm"
          onClick={onSaveDesign}
        >
          Save to Post
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={onClose}
        >
          Close
        </Button>
      </div>
    </div>
  );
};
